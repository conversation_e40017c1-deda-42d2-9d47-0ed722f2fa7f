/**
 * Docker Integration API for Linux System
 * 
 * Manages Docker containers with integrated Linux System API
 */

import { NextRequest, NextResponse } from 'next/server';
import { DockerIntegration } from '@/lib/linux-system-api/docker/docker-integration';

const dockerIntegration = new DockerIntegration();

/**
 * GET /api/linux-system/docker
 * List containers or get container information
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const action = url.searchParams.get('action');
    const containerId = url.searchParams.get('id');
    const all = url.searchParams.get('all') === 'true';

    switch (action) {
      case 'list':
        const containers = await dockerIntegration.listContainers(all);
        return NextResponse.json({
          containers,
          count: containers.length
        });

      case 'info':
        if (!containerId) {
          return NextResponse.json(
            { error: 'Container ID is required' },
            { status: 400 }
          );
        }
        const containerInfo = await dockerIntegration.getContainerInfo(containerId);
        return NextResponse.json(containerInfo);

      case 'system-info':
        if (!containerId) {
          return NextResponse.json(
            { error: 'Container ID is required' },
            { status: 400 }
          );
        }
        const systemInfo = await dockerIntegration.getContainerSystemInfo(containerId);
        return NextResponse.json(systemInfo);

      case 'logs':
        if (!containerId) {
          return NextResponse.json(
            { error: 'Container ID is required' },
            { status: 400 }
          );
        }
        const tail = parseInt(url.searchParams.get('tail') || '100');
        const since = url.searchParams.get('since') || undefined;
        const logs = await dockerIntegration.getContainerLogs(containerId, { tail, since });
        return NextResponse.json({ logs });

      default:
        return NextResponse.json({
          message: 'Docker Integration API',
          endpoints: [
            'GET /api/linux-system/docker?action=list&all=true - List containers',
            'GET /api/linux-system/docker?action=info&id=<id> - Get container info',
            'GET /api/linux-system/docker?action=system-info&id=<id> - Get system info',
            'GET /api/linux-system/docker?action=logs&id=<id>&tail=100 - Get logs',
            'POST /api/linux-system/docker - Create/manage containers',
            'PUT /api/linux-system/docker - Start/stop containers',
            'DELETE /api/linux-system/docker - Remove containers'
          ],
          availableImages: [
            'node-microvm - Node.js development container',
            'vibekraft/sandbox-base - Full development environment',
            'desktop-vm - Desktop environment with VNC',
            'ubuntu:22.04 - Base Ubuntu container',
            'alpine:latest - Base Alpine container'
          ]
        });
    }
  } catch (error) {
    console.error('Docker API error:', error);
    return NextResponse.json(
      { 
        error: 'Docker operation failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/linux-system/docker
 * Create containers or execute commands
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, ...options } = body;

    switch (action) {
      case 'create':
        const { image, config = {} } = options;
        if (!image) {
          return NextResponse.json(
            { error: 'Image name is required' },
            { status: 400 }
          );
        }

        const container = await dockerIntegration.createContainer({
          image,
          ...config
        });

        return NextResponse.json({
          success: true,
          container,
          message: `Container created successfully: ${container.name}`
        });

      case 'create-from-existing':
        const { imageName, containerConfig = {} } = options;
        if (!imageName) {
          return NextResponse.json(
            { error: 'Image name is required' },
            { status: 400 }
          );
        }

        const existingContainer = await dockerIntegration.createFromExistingImage(
          imageName, 
          containerConfig
        );

        return NextResponse.json({
          success: true,
          container: existingContainer,
          message: `Container created from ${imageName}: ${existingContainer.name}`
        });

      case 'execute':
        const { containerId, command, execOptions = {} } = options;
        if (!containerId || !command) {
          return NextResponse.json(
            { error: 'Container ID and command are required' },
            { status: 400 }
          );
        }

        const result = await dockerIntegration.executeInContainer(
          containerId, 
          command, 
          execOptions
        );

        return NextResponse.json({
          success: true,
          result,
          command
        });

      case 'install-package':
        const { containerId: pkgContainerId, packageName, packageOptions = {} } = options;
        if (!pkgContainerId || !packageName) {
          return NextResponse.json(
            { error: 'Container ID and package name are required' },
            { status: 400 }
          );
        }

        await dockerIntegration.installPackageInContainer(
          pkgContainerId, 
          packageName, 
          packageOptions
        );

        return NextResponse.json({
          success: true,
          message: `Package ${packageName} installed successfully in container ${pkgContainerId}`
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: create, create-from-existing, execute, install-package' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Docker POST error:', error);
    return NextResponse.json(
      { 
        error: 'Docker operation failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/linux-system/docker
 * Start, stop, or restart containers
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, containerId } = body;

    if (!containerId) {
      return NextResponse.json(
        { error: 'Container ID is required' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'start':
        await dockerIntegration.startContainer(containerId);
        return NextResponse.json({
          success: true,
          message: `Container ${containerId} started successfully`
        });

      case 'stop':
        await dockerIntegration.stopContainer(containerId);
        return NextResponse.json({
          success: true,
          message: `Container ${containerId} stopped successfully`
        });

      case 'restart':
        await dockerIntegration.stopContainer(containerId);
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
        await dockerIntegration.startContainer(containerId);
        return NextResponse.json({
          success: true,
          message: `Container ${containerId} restarted successfully`
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: start, stop, restart' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Docker PUT error:', error);
    return NextResponse.json(
      { 
        error: 'Docker operation failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/linux-system/docker
 * Remove containers
 */
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const containerId = url.searchParams.get('id');
    const force = url.searchParams.get('force') === 'true';

    if (!containerId) {
      return NextResponse.json(
        { error: 'Container ID is required' },
        { status: 400 }
      );
    }

    await dockerIntegration.removeContainer(containerId, force);

    return NextResponse.json({
      success: true,
      message: `Container ${containerId} removed successfully`
    });
  } catch (error) {
    console.error('Docker DELETE error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to remove container',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
