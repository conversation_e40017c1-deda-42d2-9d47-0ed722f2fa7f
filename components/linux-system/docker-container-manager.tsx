/**
 * Docker Container Manager Component
 * 
 * Manages Docker containers with integrated Linux System API
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Container, 
  Play, 
  Square, 
  RotateCcw, 
  Trash2, 
  Terminal, 
  Package,
  Info,
  RefreshCw,
  Plus,
  AlertTriangle
} from 'lucide-react';
import { useDockerIntegration } from '@/lib/linux-system-api/hooks/use-docker-integration';

export function DockerContainerManager() {
  const {
    containers,
    isLoading,
    error,
    createFromExistingImage,
    startContainer,
    stopContainer,
    restartContainer,
    removeContainer,
    executeInContainer,
    installPackageInContainer,
    getContainerLogs,
    refreshContainers
  } = useDockerIntegration({ autoRefresh: true, refreshInterval: 10000 });

  const [selectedImage, setSelectedImage] = useState('');
  const [containerName, setContainerName] = useState('');
  const [commandInput, setCommandInput] = useState('');
  const [selectedContainer, setSelectedContainer] = useState('');
  const [commandOutput, setCommandOutput] = useState('');
  const [packageName, setPackageName] = useState('');
  const [logs, setLogs] = useState('');

  // Available images from your project
  const availableImages = [
    { value: 'node-microvm', label: 'Node.js MicroVM', description: 'Node.js development container' },
    { value: 'vibekraft/sandbox-base', label: 'VibeKraft Sandbox', description: 'Full development environment' },
    { value: 'desktop-vm', label: 'Desktop VM', description: 'Desktop environment with VNC' },
    { value: 'ubuntu:22.04', label: 'Ubuntu 22.04', description: 'Base Ubuntu container' },
    { value: 'alpine:latest', label: 'Alpine Linux', description: 'Lightweight Alpine container' }
  ];

  const handleCreateContainer = async () => {
    if (!selectedImage) return;

    try {
      const config = {
        name: containerName || undefined,
        ports: getDefaultPorts(selectedImage),
        environment: getDefaultEnvironment(selectedImage)
      };

      await createFromExistingImage(selectedImage, config);
      setSelectedImage('');
      setContainerName('');
    } catch (err) {
      console.error('Failed to create container:', err);
    }
  };

  const handleExecuteCommand = async () => {
    if (!selectedContainer || !commandInput.trim()) return;

    try {
      const result = await executeInContainer(selectedContainer, commandInput);
      setCommandOutput(prev => 
        `${prev}\n$ ${commandInput}\n${result.stdout}${result.stderr ? `\nERROR: ${result.stderr}` : ''}\n`
      );
      setCommandInput('');
    } catch (err) {
      setCommandOutput(prev => 
        `${prev}\n$ ${commandInput}\nERROR: ${err instanceof Error ? err.message : 'Unknown error'}\n`
      );
    }
  };

  const handleInstallPackage = async () => {
    if (!selectedContainer || !packageName.trim()) return;

    try {
      await installPackageInContainer(selectedContainer, packageName, { noConfirm: true });
      setPackageName('');
      setCommandOutput(prev => `${prev}\nPackage ${packageName} installed successfully\n`);
    } catch (err) {
      setCommandOutput(prev => 
        `${prev}\nFailed to install ${packageName}: ${err instanceof Error ? err.message : 'Unknown error'}\n`
      );
    }
  };

  const handleGetLogs = async (containerId: string) => {
    try {
      const containerLogs = await getContainerLogs(containerId, { tail: 100 });
      setLogs(containerLogs);
    } catch (err) {
      console.error('Failed to get logs:', err);
    }
  };

  const getDefaultPorts = (image: string) => {
    const portMappings: { [key: string]: any } = {
      'node-microvm': { '3000': '3000', '22': '2222' },
      'vibekraft/sandbox-base': { '3000': '3000', '5901': '5901', '6080': '6080' },
      'desktop-vm': { '5901': '5901', '6080': '6080' },
      'ubuntu:22.04': { '22': '2222' },
      'alpine:latest': { '22': '2222' }
    };
    return portMappings[image] || {};
  };

  const getDefaultEnvironment = (image: string) => {
    const envMappings: { [key: string]: any } = {
      'vibekraft/sandbox-base': { 
        DISPLAY: ':1',
        VNC_PASSWORD: 'vibekraft',
        RESOLUTION: '1280x800'
      },
      'desktop-vm': {
        DISPLAY: ':1',
        VNC_PASSWORD: 'vncpassword',
        RESOLUTION: '1280x720'
      }
    };
    return envMappings[image] || {};
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'default';
      case 'stopped': return 'secondary';
      case 'paused': return 'outline';
      case 'restarting': return 'destructive';
      default: return 'secondary';
    }
  };

  if (error) {
    return (
      <Alert className="m-4">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Failed to connect to Docker: {error}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Docker Container Manager</h1>
          <p className="text-muted-foreground">
            Manage Docker containers with integrated Linux System API
          </p>
        </div>
        <Button onClick={refreshContainers} disabled={isLoading} size="sm">
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      <Tabs defaultValue="containers" className="space-y-4">
        <TabsList>
          <TabsTrigger value="containers">
            <Container className="h-4 w-4 mr-2" />
            Containers
          </TabsTrigger>
          <TabsTrigger value="create">
            <Plus className="h-4 w-4 mr-2" />
            Create
          </TabsTrigger>
          <TabsTrigger value="manage">
            <Terminal className="h-4 w-4 mr-2" />
            Manage
          </TabsTrigger>
        </TabsList>

        {/* Containers List */}
        <TabsContent value="containers">
          <Card>
            <CardHeader>
              <CardTitle>Running Containers</CardTitle>
              <CardDescription>
                {containers.length} container(s) found
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-4">
                  {containers.map((container) => (
                    <div key={container.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h3 className="font-medium">{container.name}</h3>
                          <Badge variant={getStatusColor(container.status)}>
                            {container.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{container.image}</p>
                        <p className="text-xs text-muted-foreground">ID: {container.id}</p>
                        {Object.keys(container.ports).length > 0 && (
                          <p className="text-xs text-muted-foreground">
                            Ports: {Object.entries(container.ports).map(([container, host]) => `${host}:${container}`).join(', ')}
                          </p>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        {container.status === 'running' ? (
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => stopContainer(container.id)}
                          >
                            <Square className="h-4 w-4" />
                          </Button>
                        ) : (
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => startContainer(container.id)}
                          >
                            <Play className="h-4 w-4" />
                          </Button>
                        )}
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => restartContainer(container.id)}
                        >
                          <RotateCcw className="h-4 w-4" />
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleGetLogs(container.id)}
                        >
                          <Info className="h-4 w-4" />
                        </Button>
                        <Button 
                          size="sm" 
                          variant="destructive"
                          onClick={() => removeContainer(container.id, true)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                  {containers.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      No containers found. Create one to get started.
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Create Container */}
        <TabsContent value="create">
          <Card>
            <CardHeader>
              <CardTitle>Create New Container</CardTitle>
              <CardDescription>
                Create a container from your existing images
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">Select Image</label>
                <Select value={selectedImage} onValueChange={setSelectedImage}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose an image" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableImages.map((image) => (
                      <SelectItem key={image.value} value={image.value}>
                        <div>
                          <div className="font-medium">{image.label}</div>
                          <div className="text-sm text-muted-foreground">{image.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-sm font-medium">Container Name (optional)</label>
                <Input
                  placeholder="Enter container name..."
                  value={containerName}
                  onChange={(e) => setContainerName(e.target.value)}
                />
              </div>

              <Button 
                onClick={handleCreateContainer} 
                disabled={!selectedImage}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Container
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Manage Container */}
        <TabsContent value="manage">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Command Execution */}
            <Card>
              <CardHeader>
                <CardTitle>Execute Commands</CardTitle>
                <CardDescription>
                  Run commands inside containers
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Select value={selectedContainer} onValueChange={setSelectedContainer}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select container" />
                  </SelectTrigger>
                  <SelectContent>
                    {containers.filter(c => c.status === 'running').map((container) => (
                      <SelectItem key={container.id} value={container.id}>
                        {container.name} ({container.image})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <div className="flex space-x-2">
                  <Input
                    placeholder="Enter command..."
                    value={commandInput}
                    onChange={(e) => setCommandInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleExecuteCommand()}
                    className="flex-1"
                  />
                  <Button onClick={handleExecuteCommand} disabled={!selectedContainer}>
                    <Terminal className="h-4 w-4" />
                  </Button>
                </div>

                <div className="flex space-x-2">
                  <Input
                    placeholder="Package name..."
                    value={packageName}
                    onChange={(e) => setPackageName(e.target.value)}
                    className="flex-1"
                  />
                  <Button onClick={handleInstallPackage} disabled={!selectedContainer}>
                    <Package className="h-4 w-4" />
                  </Button>
                </div>

                <ScrollArea className="h-48 w-full border rounded-md p-4">
                  <pre className="text-sm font-mono whitespace-pre-wrap">
                    {commandOutput || 'No commands executed yet...'}
                  </pre>
                </ScrollArea>
              </CardContent>
            </Card>

            {/* Container Logs */}
            <Card>
              <CardHeader>
                <CardTitle>Container Logs</CardTitle>
                <CardDescription>
                  View container output and logs
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-64 w-full border rounded-md p-4">
                  <pre className="text-sm font-mono whitespace-pre-wrap">
                    {logs || 'Click the info button on a container to view logs...'}
                  </pre>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
