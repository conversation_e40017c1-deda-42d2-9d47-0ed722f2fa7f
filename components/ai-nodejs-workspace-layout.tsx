/**
 * AI Node.js Workspace Layout
 *
 * A specialized workspace layout for AI-powered Node.js application generation
 * that integrates the AI generator, devbot assistant, and Nodebox runtime components.
 */

"use client";

import * as React from "react";
import { useEffect, useState, useMemo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { TabGroup } from "@/components/ui/tabs-layout";
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from "@/components/ui/resizable";
import { SidePanel } from "@/components/ui/side-panel";
import {
  Code2,
  Monitor,
  Files,
  Terminal,
  Sparkles,
  Bot,
  Cpu,
  MonitorSpeaker,
  Server,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useWorkspaceStore } from "@/lib/stores/workspace-store";
import { useAgentStore } from "@/lib/stores/agent-store";
// Remove unused import

// Import AI and Nodebox components
import { NodeboxWorkspace } from "@/lib/nodebox-runtime/components/nodebox-workspace";
import { NodeboxTerminal } from "@/lib/nodebox-runtime/components/nodebox-terminal";
import { NodeboxPreview } from "@/lib/nodebox-runtime/components/nodebox-preview";
import { useNodeboxIntegration } from "@/lib/stores/nodebox-store";
import { useNodeboxAIIntegration } from "@/lib/nodebox-ai";

// Import Sandpack components
import { SandpackWorkspaceIntegration } from "@/lib/sandpack-runtime/components/sandpack-workspace-integration";
import { useSandpackIntegration } from "@/lib/stores/sandpack-store";
import { TranscriptProvider } from "./dev-bot/TranscriptContext";
import { ToolsProvider } from "./dev-bot/ToolsContext";
import VKLogo from "./icons/VkLogoGradient";
import { UserMenu } from "./ui/user-menu";
import { AgenticChatInterface } from "./agentic-chatbot";
import { DesktopVMInterface } from "./desktop-vm";
// Remove unused import
import { useSearchParams } from "next/navigation";
import { CodeEditorPanel } from "./code-editor-panel";
import { EnhancedProjectDashboard } from "./nodebox/enhanced-project-dashboard";

// Import extension system
import {
  ExtensionProvider,
  useMergedMainTabs,
  useMergedPanelTabs,
  ExtensionDashboard
} from "@/lib/workspace-extensions";

interface AINodejsWorkspaceLayoutProps {
  className?: string;
  projectId?: string;
}

/**
 * Enhanced workspace component with extension support
 */
function AINodejsWorkspaceLayoutCore({
  className,
  projectId,
}: AINodejsWorkspaceLayoutProps & { projectId: string }) {
  // Get state and actions from the workspace store
  const {

    // Tab management
    activeTabId,
    setActiveTabId,
    removeTab,

    // Left Panel tabs
    activeLeftPanelTab,
    setActiveLeftPanelTab,

    // Right Panel tabs
    activeRightPanelTab,
    setActiveRightPanelTab,

    // Panel visibility state
    toggleLeftPanel,
    toggleRightPanel,
  } = useWorkspaceStore();

  // Get search params for fallback if projectId not provided
  const searchParams = useSearchParams();
  const fallbackProjectId = searchParams?.get("project_id") || "";
  const effectiveProjectId = projectId || fallbackProjectId;

  // Agent state
  const { setActiveAgent } = useAgentStore() as unknown as {
    setActiveAgent: (agent: string) => void;
  };

  // Nodebox integration with the new store
  const {
    activeInstance,
    isLoading: nodeboxLoading,
    error: nodeboxError,
    // Remove unused destructuring
  } = useNodeboxIntegration(effectiveProjectId);

  // Sandpack integration
  const {
    instances: sandpackInstances,
    activeInstance: activeSandpackInstance,
    isLoading: sandpackLoading,
    error: sandpackError,
  } = useSandpackIntegration(effectiveProjectId);

  // AI Integration service for seamless component coordination
  useNodeboxAIIntegration(effectiveProjectId);

  // Local state
  const [generationStatus] = useState<'idle' | 'generating' | 'completed' | 'error'>('idle');
  const [generatedAppInfo, setGeneratedAppInfo] = useState<{
    name: string;
    type: string;
    instanceId?: string;
  } | null>(null);

  // Default active tabs
  const [localActiveTabId, setLocalActiveTabId] = useState('dashboard');
  const [localActiveLeftPanelTab, setLocalActiveLeftPanelTab] = useState('devbot');
  const [localActiveRightPanelTab, setLocalActiveRightPanelTab] = useState('sandpack-manager');
  const [isLeftPanelVisible] = useState(true);
  const [isRightPanelVisible] = useState(false);

  // Set agent to ai-nodejs on mount and initialize default tabs
  useEffect(() => {
    setActiveAgent("ai-nodejs");

    // Set default active tabs if not already set
    if (!activeTabId) {
      setActiveTabId('dashboard');
    }
    setActiveLeftPanelTab('devbot');
    setActiveRightPanelTab('sandpack-manager');
  }, [setActiveAgent, setActiveTabId, setActiveLeftPanelTab, setActiveRightPanelTab]); // Remove activeTabId and activeLeftPanelTab from deps

  // Initialize panels to be visible by default
  useEffect(() => {
    // Show left panel by default for AI Node.js workspace
    if (!isLeftPanelVisible) {
      toggleLeftPanel();
    }
  }, []); // Only run once on mount



  // Remove unused function

  // Tab management functions
  const getCurrentActiveTabId = () => activeTabId || localActiveTabId;
  const getCurrentActiveLeftPanelTab = () => activeLeftPanelTab || localActiveLeftPanelTab;
  const getCurrentActiveRightPanelTab = () => activeRightPanelTab || localActiveRightPanelTab;

  const handleMainTabChange = (tabId: string) => {
    setActiveTabId(tabId);
    setLocalActiveTabId(tabId);
  };

  const handleLeftPanelTabChange = (tabId: string) => {
    setActiveLeftPanelTab(tabId);
    setLocalActiveLeftPanelTab(tabId);
  };

  const handleRightPanelTabChange = (tabId: string) => {
    setActiveRightPanelTab(tabId);
    setLocalActiveRightPanelTab(tabId);
  };

  // Handle tab close
  const handleTabClose = (tabId: string) => {
    removeTab(tabId);
    // If the closed tab was active, switch to the first available tab
    if (getCurrentActiveTabId() === tabId) {
      const remainingTabs = mainTabs.filter(tab => tab.id !== tabId);
      if (remainingTabs.length > 0) {
        handleMainTabChange(remainingTabs[0].id);
      }
    }
  };

  // Remove unused function

  // Base main tabs configuration - memoized to prevent infinite loops
  const baseMainTabs = useMemo(() => [
    {
      id: 'dashboard',
      title: 'Dashboard',
      icon: <Sparkles className="h-4 w-4" />,
      content: (
        <div className="h-full w-full overflow-auto">
          <div className="space-y-6">
            <EnhancedProjectDashboard projectId={effectiveProjectId} />
            <ExtensionDashboard projectId={effectiveProjectId} />
          </div>
        </div>
      ),
    },
    {
      id: 'nodebox-workspace',
      title: 'Workspace',
      icon: <Code2 className="h-4 w-4" />,
      content: (
        <div className="h-full w-full">
          <NodeboxWorkspace
            projectId={effectiveProjectId}
            instanceId={generatedAppInfo?.instanceId}
            className="h-full w-full"
            onInstanceChange={(instance) => {
              if (instance && generatedAppInfo) {
                setGeneratedAppInfo({
                  ...generatedAppInfo,
                  instanceId: instance.id,
                });
              }
            }}
          />
        </div>
      ),
    },
    {
      id: 'code-editor',
      title: 'Editor',
      icon: <Files className="h-4 w-4" />,
      content: (
        <CodeEditorPanel projectId={effectiveProjectId} />
      ),
    },
    {
      id: 'terminal',
      title: 'Terminal',
      icon: <Terminal className="h-4 w-4" />,
      content: (
        <div className="h-full w-full">
          {activeInstance ? (
            <NodeboxTerminal
              instanceId={activeInstance.id}
              className="h-full w-full"
            />
          ) : nodeboxError ? (
            <div className="flex items-center justify-center h-full w-full text-muted-foreground">
              <div className="text-center">
                <Terminal className="h-8 w-8 mx-auto mb-2" />
                <p>Nodebox Runtime Unavailable</p>
                <p className="text-sm">Terminal requires Nodebox runtime</p>
                <p className="text-xs mt-2 text-red-500">Error: {nodeboxError.message}</p>
              </div>
            </div>
          ) : nodeboxLoading ? (
            <div className="flex items-center justify-center h-full w-full text-muted-foreground">
              <div className="text-center">
                <Terminal className="h-8 w-8 mx-auto mb-2 animate-pulse" />
                <p>Connecting to Nodebox...</p>
                <p className="text-sm">Please wait</p>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full w-full text-muted-foreground">
              <div className="text-center">
                <Terminal className="h-8 w-8 mx-auto mb-2" />
                <p>No active Nodebox instance</p>
                <p className="text-sm">Generate an app to use terminal</p>
              </div>
            </div>
          )}
        </div>
      ),
    },
    {
      id: 'preview',
      title: 'Preview',
      icon: <Monitor className="h-4 w-4" />,
      content: (
        <div className="h-full w-full">
          {activeInstance ? (
            <NodeboxPreview
              instanceId={activeInstance.id}
              projectId={effectiveProjectId}
              className="h-full w-full"
              onPreviewChange={(preview) => {
                console.log('Preview changed:', preview);
                // Handle preview changes if needed
              }}
              onError={(error) => {
                console.error('Preview error:', error);
                // Handle preview errors if needed
              }}
            />
          ) : nodeboxError ? (
            <div className="flex items-center justify-center h-full w-full text-muted-foreground">
              <div className="text-center">
                <Monitor className="h-8 w-8 mx-auto mb-2" />
                <p>Nodebox Runtime Unavailable</p>
                <p className="text-sm">Preview requires Nodebox runtime</p>
                <p className="text-xs mt-2 text-red-500">Error: {nodeboxError.message}</p>
              </div>
            </div>
          ) : nodeboxLoading ? (
            <div className="flex items-center justify-center h-full w-full text-muted-foreground">
              <div className="text-center">
                <Monitor className="h-8 w-8 mx-auto mb-2 animate-pulse" />
                <p>Connecting to Nodebox...</p>
                <p className="text-sm">Please wait</p>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full w-full text-muted-foreground">
              <div className="text-center">
                <Monitor className="h-8 w-8 mx-auto mb-2" />
                <p>No active Nodebox instance</p>
                <p className="text-sm">Generate an app to see preview</p>
              </div>
            </div>
          )}
        </div>
      ),
    },
    {
      id: 'desktop-vm',
      title: 'Desktop VM',
      icon: <MonitorSpeaker className="h-4 w-4" />,
      content: (
        <div className="h-full w-full">
          <DesktopVMInterface
            projectId={effectiveProjectId}
            autoStart={false}
            className="h-full w-full"
          />
        </div>
      ),
    },
    {
      id: 'sandpack',
      title: 'Sandpack',
      icon: <Cpu className="h-4 w-4" />,
      content: (
        <div className="h-full w-full">
          <SandpackWorkspaceIntegration
            projectId={effectiveProjectId}
            className="h-full w-full"
          />
        </div>
      ),
    },
  ], [effectiveProjectId, generatedAppInfo, activeInstance, nodeboxError, nodeboxLoading]);

  // Merge base tabs with extension tabs
  const mainTabs = useMergedMainTabs(effectiveProjectId, baseMainTabs);

  // Create base panel tabs for this workspace - memoized to prevent infinite loops
  const baseLeftPanelTabs = useMemo(() => [
    {
      id: 'devbot',
      title: 'AI Assistant',
      icon: <Bot className="h-4 w-4" />,
      content: (
        <AgenticChatInterface
          projectId={effectiveProjectId}
          apiEndpoint="/api/nodebox-ai"
          systemPrompt={`You are an AI development assistant with comprehensive Nodebox and Sandpack capabilities and intelligent runtime tools.

Current Context:
- Project ID: ${effectiveProjectId}
- Active Nodebox Instance: ${activeInstance?.config.name || 'None'}
- Nodebox Status: ${nodeboxError ? 'Error' : nodeboxLoading ? 'Loading' : activeInstance ? 'Connected' : 'Disconnected'}
- Sandpack Status: ${sandpackError ? 'Error' : sandpackLoading ? 'Loading' : activeSandpackInstance ? 'Active' : 'Ready'}
- Active Sandpack Instances: ${sandpackInstances.length}
- Generation Status: ${generationStatus}

Available Capabilities:
🔧 Basic Nodebox Tools: read_file_nodebox, write_file_nodebox, create_file_nodebox, list_files_nodebox, run_command_nodebox
📦 Intelligent Runtime Tools: create_project_from_template, install_packages_intelligent, run_dev_server_with_preview
🌐 Web Tools: scrape_webpage, search_web, interact_with_webpage, take_screenshot
🤖 Agentic Tools: analyze_task_complexity, orchestrate_complex_task, evaluate_generated_code
🏖️ Sandpack Tools: Create and manage Sandpack instances for browser-based development with live preview

Runtime Options:
- Nodebox: Browser-based Node.js runtime with filesystem and shell implementation
- Sandpack: CodeSandbox-powered browser development environment with live preview and hot reloading

Use these tools to help with file operations, project management, development tasks, web research, and complex task orchestration. You can recommend using Sandpack for interactive development with live preview or Nodebox for simpler browser-based development. Always consider the current project context when making suggestions.`}
          enableTools={true}
          maxSteps={25}
          enableContextManager={true}
        />
      ),
    },
    {
      id: 'sandpack-assistant',
      title: 'Sandpack AI',
      icon: <Cpu className="h-4 w-4" />,
      content: (
        <AgenticChatInterface
          projectId={effectiveProjectId}
          apiEndpoint="/api/sandpack-ai"
          systemPrompt={`You are a specialized Sandpack development assistant.

Current Context:
- Project ID: ${effectiveProjectId}
- Sandpack Status: ${sandpackError ? 'Error' : sandpackLoading ? 'Loading' : activeSandpackInstance ? 'Active' : 'Ready'}
- Active Sandpack Instances: ${sandpackInstances.length}
${activeSandpackInstance ? `- Active Instance: ${activeSandpackInstance.name} (${activeSandpackInstance.status})` : ''}

Focus on Sandpack-specific operations and provide expert guidance on browser-based development workflows with live preview and hot reloading.`}
          enableTools={true}
          maxSteps={15}
          enableContextManager={true}
        />
      ),
    },
  ], [effectiveProjectId, activeInstance, nodeboxError, nodeboxLoading, sandpackError, sandpackLoading, sandpackInstances, activeSandpackInstance, generationStatus]);

  // Merge base left panel tabs with extension tabs
  const customLeftPanelTabs = useMergedPanelTabs(effectiveProjectId, 'left', baseLeftPanelTabs);

  const baseRightPanelTabs = useMemo(() => [
    {
      id: 'sandbox-manager',
      title: 'Sandbox Manager',
      icon: <Server className="h-4 w-4" />,
      content: (
        <div className="p-4">
          <div className="space-y-4">
            <div className="border rounded-lg p-4">
              <h3 className="text-lg font-medium mb-2">Demo VM Details</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Status</span>
                  <span className="text-sm font-medium text-green-500">Running</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">CPU Usage</span>
                  <span className="text-sm">45%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Memory</span>
                  <span className="text-sm">2.1GB / 4GB</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Uptime</span>
                  <span className="text-sm">3h 24m</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 'sandpack-manager',
      title: 'Sandpack Manager',
      icon: <Cpu className="h-4 w-4" />,
      content: (
        <div className="p-4">
          <div className="space-y-4">
            <div className="border rounded-lg p-4">
              <h3 className="text-lg font-medium mb-2">Sandpack Status</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Active Instances</span>
                  <span className="text-sm font-medium">{sandpackInstances.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Status</span>
                  <span className={cn(
                    "text-sm font-medium",
                    sandpackError ? "text-red-500" :
                    sandpackLoading ? "text-yellow-500" :
                    activeSandpackInstance ? "text-green-500" : "text-gray-500"
                  )}>
                    {sandpackError ? "Error" :
                     sandpackLoading ? "Loading" :
                     activeSandpackInstance ? "Active" : "Ready"}
                  </span>
                </div>
                {activeSandpackInstance && (
                  <>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Active Instance</span>
                      <span className="text-sm">{activeSandpackInstance.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Instance Status</span>
                      <span className="text-sm">{activeSandpackInstance.status}</span>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      ),
    },
  ], [webContainers, activeWebContainer, webContainerError, webContainerLoading]);

  // Merge base right panel tabs with extension tabs
  const customRightPanelTabs = useMergedPanelTabs(effectiveProjectId, 'right', baseRightPanelTabs);





  return (
    <TranscriptProvider>
      <ToolsProvider>
        <div className={cn("flex h-full w-full flex-col", className)}>
            {/* Header */}
            <div className="flex items-center justify-between border-b border-border px-4 py-2 bg-card text-card-foreground">
              <div className="flex items-center gap-3">
                <VKLogo
                  width={"1em"}
                  height={"1em"}
                  animate={true}
                  animationDuration={2}
                  animationDelay={0}
                  animationLoop={true}
                  animationType={"draw"}
                  strokeColor={"currentColor"}
                  strokeWidth={1}
                  animationTrigger={"onMount"}
                />
                <div className="h-4 w-[1px] bg-border"></div>
                <h2 className="text-sm font-medium">AI Node.js Workspace</h2>
                <span className="text-xs px-1.5 py-0.5 rounded-full bg-muted text-muted-foreground">
                  {effectiveProjectId}
                </span>

                {/* Generation Status */}
                <div className="h-4 w-[1px] bg-border ml-2"></div>
                <div className="flex items-center gap-1 ml-2">
                  <span className="text-xs font-medium">Status:</span>
                  <span
                    className={cn(
                      "text-xs px-1.5 py-0.5 rounded-full",
                      generationStatus === "idle"
                        ? "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
                        : generationStatus === "generating"
                        ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
                        : generationStatus === "completed"
                        ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                        : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                    )}
                  >
                    {generationStatus === "idle"
                      ? "Ready"
                      : generationStatus === "generating"
                      ? "Generating..."
                      : generationStatus === "completed"
                      ? "Completed"
                      : "Error"}
                  </span>
                </div>

                {/* Nodebox Status */}
                <div className="h-4 w-[1px] bg-border ml-2"></div>
                <div className="flex items-center gap-1 ml-2">
                  <span className="text-xs font-medium">Runtime:</span>
                  <span
                    className={cn(
                      "text-xs px-1.5 py-0.5 rounded-full",
                      nodeboxError
                        ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                        : nodeboxLoading
                        ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                        : activeInstance
                        ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                        : "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
                    )}
                  >
                    {nodeboxError
                      ? "Error"
                      : nodeboxLoading
                      ? "Connecting..."
                      : activeInstance
                      ? "Connected"
                      : "Disconnected"}
                  </span>
                </div>

                {/* Active Instance Info */}
                {activeInstance && (
                  <>
                    <div className="h-4 w-[1px] bg-border ml-2"></div>
                    <div className="flex items-center gap-1 ml-2">
                      <Cpu className="h-3 w-3" />
                      <span className="text-xs font-medium">{activeInstance.config.name}</span>
                    </div>
                  </>
                )}

                {/* WebContainer Status */}
                <div className="h-4 w-[1px] bg-border ml-2"></div>
                <div className="flex items-center gap-1 ml-2">
                  <span className="text-xs font-medium">WebContainer:</span>
                  <span
                    className={cn(
                      "text-xs px-1.5 py-0.5 rounded-full",
                      webContainerError
                        ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                        : webContainerLoading
                        ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                        : activeWebContainer
                        ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                        : "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
                    )}
                  >
                    {webContainerError
                      ? "Error"
                      : webContainerLoading
                      ? "Loading..."
                      : activeWebContainer
                      ? `${webContainers.length} Active`
                      : "Ready"}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <div className="flex items-center mr-2 border-r border-border pr-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "px-2 py-1",
                      isLeftPanelVisible ? "bg-muted" : "bg-transparent"
                    )}
                    onClick={toggleLeftPanel}
                  >
                    <Bot className="h-4 w-4" />
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "px-2 py-1",
                      isRightPanelVisible ? "bg-muted" : "bg-transparent"
                    )}
                    onClick={toggleRightPanel}
                  >
                    <Bot className="h-4 w-4" />
                  </Button>
                </div>
                <UserMenu />
              </div>
            </div>

            {/* Main Content */}
            <div className="flex-1 overflow-hidden">
              <ResizablePanelGroup direction="horizontal">
                {/* Left Panel - AI Generator */}
                {isLeftPanelVisible && (
                  <>
                    <ResizablePanel defaultSize={25} minSize={20} maxSize={40}>
                      <SidePanel
                        position="left"
                        isVisible={isLeftPanelVisible}
                        onToggle={toggleLeftPanel}
                        tabs={customLeftPanelTabs}
                        activeTabId={getCurrentActiveLeftPanelTab()}
                        onTabChange={handleLeftPanelTabChange}
                        defaultSize={25}
                        minSize={20}
                        maxSize={40}
                        className="bg-card h-full max-w-fit "
                      >
                        {customLeftPanelTabs.find(tab => tab.id === getCurrentActiveLeftPanelTab())?.content}
                      </SidePanel>
                    </ResizablePanel>
                    <ResizableHandle withHandle />
                  </>
                )}

                {/* Main Content Area - Tabbed Interface */}
                <ResizablePanel defaultSize={isLeftPanelVisible ? 75 : 100}>
                  <div className="flex flex-col h-full">
                    {/* Tab Headers */}
                    <TabGroup
                      tabs={mainTabs}
                      activeTabId={getCurrentActiveTabId()}
                      onTabChange={handleMainTabChange}
                      onTabClose={handleTabClose}
                      groupId="main-tabs"
                      className="flex-shrink-0"
                    />

                    {/* Tab Content */}
                    <div className="flex-1 overflow-hidden h-full w-full">
                      <div className="h-full w-full">
                        {mainTabs.find(tab => tab.id === getCurrentActiveTabId())?.content}
                      </div>
                    </div>
                  </div>
                </ResizablePanel>

                {/* Right Panel - Sandbox Manager */}
                {isRightPanelVisible && (
                  <>
                    <ResizableHandle withHandle />
                    <ResizablePanel defaultSize={25} minSize={20} maxSize={40}>
                      <SidePanel
                        position="right"
                        isVisible={isRightPanelVisible}
                        onToggle={toggleRightPanel}
                        tabs={customRightPanelTabs}
                        activeTabId={getCurrentActiveRightPanelTab()}
                        onTabChange={handleRightPanelTabChange}
                        defaultSize={25}
                        minSize={20}
                        maxSize={40}
                        className="bg-card h-full"
                      >
                        {customRightPanelTabs.find(tab => tab.id === getCurrentActiveRightPanelTab())?.content}
                      </SidePanel>
                    </ResizablePanel>
                  </>
                )}


              </ResizablePanelGroup>
            </div>
        </div>
      </ToolsProvider>
    </TranscriptProvider>
  );
}

/**
 * Main workspace layout component with extension support
 */
export function AINodejsWorkspaceLayout({
  className,
}: AINodejsWorkspaceLayoutProps) {
  // Get search params for project ID
  const searchParams = useSearchParams();
  const projectId = searchParams?.get("project_id") || "";

  return (
    <ExtensionProvider projectId={projectId}>
      <AINodejsWorkspaceLayoutCore
        className={className}
        projectId={projectId}
      />
    </ExtensionProvider>
  );
}

export default AINodejsWorkspaceLayout;
