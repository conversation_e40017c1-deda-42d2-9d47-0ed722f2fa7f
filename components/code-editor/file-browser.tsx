"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { cn } from "@/lib/utils"
import { FileTree, FileNode } from "@/components/file-explorer/file-tree"
import { useFileStore } from "@/lib/stores/file-store"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  FolderTree, 
  File, 
  FolderPlus, 
  FilePlus, 
  RefreshCw,
  Search
} from "lucide-react"
import { Input } from "@/components/ui/input"

interface FileBrowserProps {
  onFileSelect: (file: FileNode) => void
  className?: string
}

export function FileBrowser({ onFileSelect, className }: FileBrowserProps) {
  // Get file store
  const { 
    files, 
    selectedFilePath, 
    selectFile, 
    addFile, 
    addDirectory 
  } = useFileStore()
  
  // State for search
  const [searchQuery, setSearchQuery] = useState("")
  
  // Convert flat file list to tree structure
  const fileTree = React.useMemo(() => {
    // Create a map of paths to directories
    const dirMap: Record<string, FileNode> = {}
    
    // Create root directory
    const root: FileNode = {
      id: 'root',
      name: 'Project',
      type: 'directory',
      path: '/',
      children: []
    }
    dirMap['/'] = root
    
    // Process all files
    files.forEach(file => {
      // Skip the root directory
      if (file.path === '/') return
      
      // Get the parent directory path
      const pathParts = file.path.split('/').filter(Boolean)
      const fileName = pathParts.pop() || ''
      const parentPath = pathParts.length === 0 ? '/' : '/' + pathParts.join('/')
      
      // Ensure parent directory exists
      if (!dirMap[parentPath]) {
        // Create parent directory
        const parentName = pathParts[pathParts.length - 1] || 'Project'
        const parentDir: FileNode = {
          id: `dir-${parentPath}`,
          name: parentName,
          type: 'directory',
          path: parentPath,
          children: []
        }
        dirMap[parentPath] = parentDir
        
        // Add to its parent
        const grandParentPath = pathParts.slice(0, -1).join('/')
        const grandParentDir = dirMap[grandParentPath] || root
        grandParentDir.children = grandParentDir.children || []
        grandParentDir.children.push(parentDir)
      }
      
      // Add file to parent directory
      if (file.type === 'file') {
        const parentDir = dirMap[parentPath]
        parentDir.children = parentDir.children || []
        parentDir.children.push({
          ...file,
          name: fileName
        })
      } else if (file.type === 'directory') {
        // Add directory to parent and to dirMap
        const dirPath = file.path.endsWith('/') ? file.path : `${file.path}/`
        if (!dirMap[dirPath]) {
          const dir: FileNode = {
            ...file,
            name: fileName,
            children: []
          }
          dirMap[dirPath] = dir
          
          const parentDir = dirMap[parentPath]
          parentDir.children = parentDir.children || []
          parentDir.children.push(dir)
        }
      }
    })
    
    // Sort each directory's children
    const sortDir = (dir: FileNode) => {
      if (dir.children) {
        dir.children.sort((a, b) => {
          if (a.type === 'directory' && b.type === 'file') return -1
          if (a.type === 'file' && b.type === 'directory') return 1
          return a.name.localeCompare(b.name)
        })
        
        dir.children.forEach(child => {
          if (child.type === 'directory') {
            sortDir(child)
          }
        })
      }
    }
    
    sortDir(root)
    
    return [root]
  }, [files])
  
  // Filter files based on search query
  const filteredFileTree = React.useMemo(() => {
    if (!searchQuery) return fileTree
    
    // Helper function to filter nodes
    const filterNode = (node: FileNode): FileNode | null => {
      if (node.name.toLowerCase().includes(searchQuery.toLowerCase())) {
        return node
      }
      
      if (node.type === 'directory' && node.children) {
        const filteredChildren = node.children
          .map(filterNode)
          .filter(Boolean) as FileNode[]
        
        if (filteredChildren.length > 0) {
          return {
            ...node,
            children: filteredChildren
          }
        }
      }
      
      return null
    }
    
    return fileTree.map(filterNode).filter(Boolean) as FileNode[]
  }, [fileTree, searchQuery])
  
  // Handle file selection
  const handleFileSelect = (file: FileNode) => {
    if (file.type === 'file') {
      selectFile(file.path)
      onFileSelect(file)
    }
  }
  
  // Handle new file button click
  const handleNewFile = () => {
    const fileName = prompt('Enter file name:')
    if (fileName) {
      const path = selectedFilePath 
        ? `${selectedFilePath.substring(0, selectedFilePath.lastIndexOf('/'))}/${fileName}`
        : `/${fileName}`
      
      addFile(path, '')
    }
  }
  
  // Handle new folder button click
  const handleNewFolder = () => {
    const folderName = prompt('Enter folder name:')
    if (folderName) {
      const path = selectedFilePath 
        ? `${selectedFilePath.substring(0, selectedFilePath.lastIndexOf('/'))}/${folderName}`
        : `/${folderName}`
      
      addDirectory(path)
    }
  }
  
  return (
    <div className={cn("flex flex-col h-full border-r border-border", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-2 border-b border-border bg-muted/30">
        <div className="flex items-center">
          <FolderTree className="h-4 w-4 mr-2 text-muted-foreground" />
          <span className="text-sm font-medium">Explorer</span>
        </div>
        <div className="flex items-center gap-1">
          <Button variant="ghost" size="icon" className="h-7 w-7" onClick={handleNewFile}>
            <FilePlus className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" className="h-7 w-7" onClick={handleNewFolder}>
            <FolderPlus className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" className="h-7 w-7">
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      {/* Search */}
      <div className="p-2 border-b border-border">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search files"
            className="pl-8 h-9 text-sm"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>
      
      {/* File Tree */}
      <div className="flex-1 overflow-hidden">
        <FileTree
          files={filteredFileTree}
          onFileSelect={handleFileSelect}
          selectedFilePath={selectedFilePath || undefined}
          expandedByDefault={true}
          className="border-0 rounded-none h-full"
        />
      </div>
    </div>
  )
}
