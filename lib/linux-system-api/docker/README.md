# Docker Integration with Linux System API

This module provides seamless integration between Docker containers and the Linux System API, allowing you to manage and interact with your existing Docker images programmatically.

## 🚀 Features

### Container Management
- **Create containers** from your existing images
- **Start, stop, restart** containers
- **Remove containers** with force option
- **List and monitor** container status
- **Get detailed container information**

### System Integration
- **Execute commands** inside containers
- **Install packages** using container's package manager
- **Monitor system resources** within containers
- **Access container logs** in real-time
- **File system operations** within containers

### Your Docker Images Support
- **node-microvm** - Node.js development container
- **vibekraft/sandbox-base** - Full development environment
- **desktop-vm** - Desktop environment with VNC
- **ubuntu:22.04** - Base Ubuntu container
- **alpine:latest** - Lightweight Alpine container

## 📋 Quick Start

### 1. Using React Hook

```tsx
import { useDockerIntegration } from '@/lib/linux-system-api/hooks/use-docker-integration';

function DockerManager() {
  const {
    containers,
    createFromExistingImage,
    startContainer,
    executeInContainer
  } = useDockerIntegration();

  const handleCreateContainer = async () => {
    // Create container from your existing image
    const container = await createFromExistingImage('node-microvm', {
      name: 'my-node-app',
      ports: { '3000': '3000' },
      environment: { NODE_ENV: 'development' }
    });
    
    console.log('Container created:', container);
  };

  const handleExecuteCommand = async (containerId: string) => {
    const result = await executeInContainer(containerId, 'npm install');
    console.log('Command output:', result.stdout);
  };

  return (
    <div>
      {containers.map(container => (
        <div key={container.id}>
          <h3>{container.name}</h3>
          <p>Status: {container.status}</p>
          <button onClick={() => startContainer(container.id)}>Start</button>
        </div>
      ))}
    </div>
  );
}
```

### 2. Using Direct API

```typescript
import { DockerIntegration } from '@/lib/linux-system-api/docker/docker-integration';

const docker = new DockerIntegration();

// Create container from your VibeKraft image
const container = await docker.createFromExistingImage('vibekraft/sandbox-base', {
  name: 'my-sandbox',
  ports: { '3000': '3000', '5901': '5901', '6080': '6080' },
  environment: {
    DISPLAY: ':1',
    VNC_PASSWORD: 'mypassword',
    RESOLUTION: '1920x1080'
  }
});

// Execute commands inside the container
const result = await docker.executeInContainer(container.id, 'ls -la /workspace');
console.log(result.stdout);

// Install packages
await docker.installPackageInContainer(container.id, 'htop');
```

### 3. Using REST API

```bash
# Create container from existing image
curl -X POST http://localhost:3000/api/linux-system/docker \
  -H "Content-Type: application/json" \
  -d '{
    "action": "create-from-existing",
    "imageName": "node-microvm",
    "containerConfig": {
      "name": "my-node-app",
      "ports": {"3000": "3000"},
      "environment": {"NODE_ENV": "development"}
    }
  }'

# List containers
curl http://localhost:3000/api/linux-system/docker?action=list&all=true

# Execute command in container
curl -X POST http://localhost:3000/api/linux-system/docker \
  -H "Content-Type: application/json" \
  -d '{
    "action": "execute",
    "containerId": "container_id_here",
    "command": "npm install"
  }'

# Start container
curl -X PUT http://localhost:3000/api/linux-system/docker \
  -H "Content-Type: application/json" \
  -d '{
    "action": "start",
    "containerId": "container_id_here"
  }'
```

## 🖥️ UI Component

Use the pre-built Docker Container Manager component:

```tsx
import { DockerContainerManager } from '@/components/linux-system/docker-container-manager';

function App() {
  return <DockerContainerManager />;
}
```

Visit `/linux-system-docker` to see the full interface.

## 🔧 Configuration Examples

### Node.js MicroVM Container
```typescript
const nodeContainer = await docker.createFromExistingImage('node-microvm', {
  name: 'node-dev',
  ports: { '3000': '3000', '22': '2222' },
  environment: { NODE_ENV: 'development' },
  volumes: { '/host/project': '/workspace' },
  restartPolicy: 'unless-stopped'
});
```

### VibeKraft Sandbox Container
```typescript
const sandboxContainer = await docker.createFromExistingImage('vibekraft/sandbox-base', {
  name: 'vibekraft-dev',
  ports: { 
    '3000': '3000',  // Web app
    '5901': '5901',  // VNC
    '6080': '6080'   // noVNC web interface
  },
  environment: {
    DISPLAY: ':1',
    VNC_PASSWORD: 'secure_password',
    RESOLUTION: '1920x1080'
  },
  privileged: true,
  restartPolicy: 'unless-stopped'
});
```

### Desktop VM Container
```typescript
const desktopContainer = await docker.createFromExistingImage('desktop-vm', {
  name: 'desktop-env',
  ports: { 
    '5901': '5901',  // VNC
    '6080': '6080'   // noVNC
  },
  environment: {
    DISPLAY: ':1',
    VNC_PASSWORD: 'desktop_pass',
    RESOLUTION: '1280x720'
  },
  privileged: true,
  volumes: { '/host/data': '/home/<USER>/data' }
});
```

## 🛠️ Advanced Operations

### System Monitoring in Containers
```typescript
// Get system info from container
const systemInfo = await docker.getContainerSystemInfo(containerId);
console.log('Container system info:', systemInfo);

// Monitor container resources
const container = containers.find(c => c.id === containerId);
if (container?.systemManager) {
  const packageManager = container.systemManager.getPackageManager();
  const installedPackages = await packageManager.getInstalledPackages();
  console.log('Installed packages:', installedPackages);
}
```

### Package Management
```typescript
// Install packages in Ubuntu containers
await docker.installPackageInContainer(containerId, 'nginx', { noConfirm: true });

// Install packages in Alpine containers
await docker.installPackageInContainer(containerId, 'htop');

// Execute package manager commands directly
await docker.executeInContainer(containerId, 'apt update && apt install -y curl');
```

### File Operations
```typescript
// Copy files to container
await docker.executeInContainer(containerId, 'mkdir -p /app/data');

// Execute file operations using the container's filesystem manager
const container = containers.find(c => c.id === containerId);
if (container?.systemManager) {
  const fsManager = container.systemManager.getFilesystemManager();
  await fsManager.writeFile('/app/config.json', JSON.stringify(config));
}
```

## 🔌 API Endpoints

### Container Management
- `GET /api/linux-system/docker?action=list` - List containers
- `POST /api/linux-system/docker` - Create/manage containers
- `PUT /api/linux-system/docker` - Start/stop containers
- `DELETE /api/linux-system/docker` - Remove containers

### Container Operations
- `GET /api/linux-system/docker?action=info&id=<id>` - Get container info
- `GET /api/linux-system/docker?action=system-info&id=<id>` - Get system info
- `GET /api/linux-system/docker?action=logs&id=<id>` - Get container logs

## 🔒 Security Considerations

1. **Container Isolation**: Each container runs with its own system manager instance
2. **Command Validation**: All commands are validated before execution
3. **Resource Limits**: Containers can be configured with resource constraints
4. **Network Security**: Proper port mapping and network configuration
5. **Volume Mounting**: Secure volume mounting with appropriate permissions

## 🚀 Integration with Your Existing Images

### Your Images Are Ready!
The Docker integration automatically detects and configures:

1. **node-microvm**: 
   - Ports: 3000 (app), 22 (SSH)
   - Environment: NODE_ENV=development
   - Package Manager: apt (Ubuntu-based)

2. **vibekraft/sandbox-base**:
   - Ports: 3000 (app), 5901 (VNC), 6080 (noVNC)
   - Environment: Display, VNC password, resolution
   - Features: Full desktop environment

3. **desktop-vm**:
   - Ports: 5901 (VNC), 6080 (noVNC)
   - Environment: Desktop with VNC access
   - Features: GUI applications support

### Custom Configuration
You can override default configurations:

```typescript
const customContainer = await docker.createFromExistingImage('your-image', {
  name: 'custom-name',
  ports: { '8080': '8080' },
  environment: { CUSTOM_VAR: 'value' },
  volumes: { '/host/path': '/container/path' },
  workdir: '/app',
  user: 'developer',
  privileged: false,
  networkMode: 'bridge',
  restartPolicy: 'always'
});
```

## 🎯 Use Cases

1. **Development Environments**: Spin up isolated development containers
2. **Testing**: Create containers for testing different configurations
3. **Microservices**: Manage multiple service containers
4. **Desktop Applications**: Run GUI applications in containers
5. **CI/CD**: Automated container deployment and management
6. **Sandboxing**: Secure isolated environments for code execution

## 🔄 Real-time Features

- **Live container status updates**
- **Real-time log streaming**
- **Container resource monitoring**
- **Automatic container discovery**
- **Event-driven container management**

The Docker integration seamlessly works with your existing Docker images and provides a powerful, secure, and user-friendly way to manage containers with full Linux system capabilities!
