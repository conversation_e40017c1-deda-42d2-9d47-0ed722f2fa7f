/**
 * Docker Integration for Linux System API
 * 
 * Integrates the Linux System API with Docker containers
 * Supports both Ubuntu and Alpine-based containers
 */

import { EventEmitter } from 'events';
import { SystemManager } from '../core/system-manager';
import { CommandExecutor } from '../utils/command-executor';
import { SystemInfo, ContainerInfo } from '../types';

export interface DockerContainerConfig {
  image: string;
  name?: string;
  ports?: { [containerPort: string]: string };
  volumes?: { [hostPath: string]: string };
  environment?: { [key: string]: string };
  command?: string[];
  workdir?: string;
  user?: string;
  privileged?: boolean;
  networkMode?: string;
  restartPolicy?: 'no' | 'always' | 'unless-stopped' | 'on-failure';
}

export interface ContainerInfo {
  id: string;
  name: string;
  image: string;
  status: 'running' | 'stopped' | 'paused' | 'restarting' | 'dead';
  created: string;
  ports: { [containerPort: string]: string };
  mounts: Array<{ source: string; destination: string; type: string }>;
  networkMode: string;
  ipAddress?: string;
  systemManager?: SystemManager;
}

export class DockerIntegration extends EventEmitter {
  private commandExecutor: CommandExecutor;
  private containers = new Map<string, ContainerInfo>();

  constructor() {
    super();
    this.commandExecutor = CommandExecutor.getInstance();
  }

  /**
   * Create and start a container with Linux System API
   */
  public async createContainer(config: DockerContainerConfig): Promise<ContainerInfo> {
    try {
      const containerName = config.name || `linux-api-${Date.now()}`;
      
      // Build docker run command
      let dockerCommand = ['docker', 'run', '-d'];
      
      // Add name
      dockerCommand.push('--name', containerName);
      
      // Add ports
      if (config.ports) {
        for (const [containerPort, hostPort] of Object.entries(config.ports)) {
          dockerCommand.push('-p', `${hostPort}:${containerPort}`);
        }
      }
      
      // Add volumes
      if (config.volumes) {
        for (const [hostPath, containerPath] of Object.entries(config.volumes)) {
          dockerCommand.push('-v', `${hostPath}:${containerPath}`);
        }
      }
      
      // Add environment variables
      if (config.environment) {
        for (const [key, value] of Object.entries(config.environment)) {
          dockerCommand.push('-e', `${key}=${value}`);
        }
      }
      
      // Add other options
      if (config.workdir) dockerCommand.push('-w', config.workdir);
      if (config.user) dockerCommand.push('--user', config.user);
      if (config.privileged) dockerCommand.push('--privileged');
      if (config.networkMode) dockerCommand.push('--network', config.networkMode);
      if (config.restartPolicy) dockerCommand.push('--restart', config.restartPolicy);
      
      // Add image
      dockerCommand.push(config.image);
      
      // Add command if specified
      if (config.command) {
        dockerCommand.push(...config.command);
      }
      
      // Execute docker run
      const result = await this.commandExecutor.executeSimple(dockerCommand.join(' '));
      const containerId = result.stdout.trim();
      
      // Get container info
      const containerInfo = await this.getContainerInfo(containerId);
      
      // Create system manager for this container
      const systemManager = await this.createContainerSystemManager(containerId);
      containerInfo.systemManager = systemManager;
      
      this.containers.set(containerId, containerInfo);
      this.emit('containerCreated', containerInfo);
      
      return containerInfo;
    } catch (error) {
      this.emit('containerCreateFailed', { config, error });
      throw error;
    }
  }

  /**
   * Get container information
   */
  public async getContainerInfo(containerId: string): Promise<ContainerInfo> {
    try {
      const result = await this.commandExecutor.executeSimple(
        `docker inspect ${containerId} --format='{{json .}}'`
      );
      
      const containerData = JSON.parse(result.stdout);
      
      return {
        id: containerData.Id.substring(0, 12),
        name: containerData.Name.replace('/', ''),
        image: containerData.Config.Image,
        status: this.mapDockerStatus(containerData.State.Status),
        created: containerData.Created,
        ports: this.extractPorts(containerData.NetworkSettings.Ports),
        mounts: containerData.Mounts.map((mount: any) => ({
          source: mount.Source,
          destination: mount.Destination,
          type: mount.Type
        })),
        networkMode: containerData.HostConfig.NetworkMode,
        ipAddress: containerData.NetworkSettings.IPAddress
      };
    } catch (error) {
      throw new Error(`Failed to get container info: ${error}`);
    }
  }

  /**
   * Create a system manager for a specific container
   */
  private async createContainerSystemManager(containerId: string): Promise<SystemManager> {
    // Create a custom command executor that runs commands inside the container
    const containerCommandExecutor = new (class extends CommandExecutor {
      async executeSimple(command: string, options: any = {}) {
        const dockerCommand = `docker exec ${containerId} ${command}`;
        return super.executeSimple(dockerCommand, options);
      }
    })();

    // Create system manager with container-specific executor
    const systemManager = new (class extends SystemManager {
      constructor() {
        super({
          enableMonitoring: true,
          enableWebSocket: false, // Disable WebSocket for container instances
          enableSecurity: true
        });
        (this as any).commandExecutor = containerCommandExecutor;
      }
    })();

    await systemManager.initialize();
    return systemManager;
  }

  /**
   * Execute command in container
   */
  public async executeInContainer(
    containerId: string, 
    command: string, 
    options: { user?: string; workdir?: string; env?: string[] } = {}
  ): Promise<any> {
    let dockerCommand = `docker exec`;
    
    if (options.user) dockerCommand += ` -u ${options.user}`;
    if (options.workdir) dockerCommand += ` -w ${options.workdir}`;
    if (options.env) {
      for (const envVar of options.env) {
        dockerCommand += ` -e ${envVar}`;
      }
    }
    
    dockerCommand += ` ${containerId} ${command}`;
    
    return this.commandExecutor.executeSimple(dockerCommand);
  }

  /**
   * Get system info from container
   */
  public async getContainerSystemInfo(containerId: string): Promise<SystemInfo | null> {
    const container = this.containers.get(containerId);
    if (!container?.systemManager) {
      throw new Error('Container system manager not found');
    }
    
    return container.systemManager.getSystemInfo();
  }

  /**
   * Install package in container
   */
  public async installPackageInContainer(
    containerId: string, 
    packageName: string, 
    options: any = {}
  ): Promise<void> {
    const container = this.containers.get(containerId);
    if (!container?.systemManager) {
      throw new Error('Container system manager not found');
    }
    
    const packageManager = container.systemManager.getPackageManager();
    await packageManager.installPackage(packageName, options);
  }

  /**
   * List all containers
   */
  public async listContainers(all = false): Promise<ContainerInfo[]> {
    try {
      const command = all ? 'docker ps -a --format="{{json .}}"' : 'docker ps --format="{{json .}}"';
      const result = await this.commandExecutor.executeSimple(command);
      
      const containers: ContainerInfo[] = [];
      const lines = result.stdout.split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        try {
          const containerData = JSON.parse(line);
          containers.push({
            id: containerData.ID,
            name: containerData.Names,
            image: containerData.Image,
            status: this.mapDockerStatus(containerData.State || containerData.Status),
            created: containerData.CreatedAt,
            ports: this.parsePorts(containerData.Ports),
            mounts: [],
            networkMode: containerData.Networks || 'default'
          });
        } catch (parseError) {
          console.warn('Failed to parse container data:', parseError);
        }
      }
      
      return containers;
    } catch (error) {
      console.error('Failed to list containers:', error);
      return [];
    }
  }

  /**
   * Stop container
   */
  public async stopContainer(containerId: string): Promise<void> {
    try {
      await this.commandExecutor.executeSimple(`docker stop ${containerId}`);
      this.emit('containerStopped', { id: containerId });
    } catch (error) {
      this.emit('containerStopFailed', { id: containerId, error });
      throw error;
    }
  }

  /**
   * Start container
   */
  public async startContainer(containerId: string): Promise<void> {
    try {
      await this.commandExecutor.executeSimple(`docker start ${containerId}`);
      this.emit('containerStarted', { id: containerId });
    } catch (error) {
      this.emit('containerStartFailed', { id: containerId, error });
      throw error;
    }
  }

  /**
   * Remove container
   */
  public async removeContainer(containerId: string, force = false): Promise<void> {
    try {
      const command = force ? `docker rm -f ${containerId}` : `docker rm ${containerId}`;
      await this.commandExecutor.executeSimple(command);
      
      this.containers.delete(containerId);
      this.emit('containerRemoved', { id: containerId });
    } catch (error) {
      this.emit('containerRemoveFailed', { id: containerId, error });
      throw error;
    }
  }

  /**
   * Get container logs
   */
  public async getContainerLogs(
    containerId: string, 
    options: { tail?: number; follow?: boolean; since?: string } = {}
  ): Promise<string> {
    let command = `docker logs ${containerId}`;
    
    if (options.tail) command += ` --tail ${options.tail}`;
    if (options.follow) command += ` --follow`;
    if (options.since) command += ` --since ${options.since}`;
    
    const result = await this.commandExecutor.executeSimple(command);
    return result.stdout;
  }

  /**
   * Create container from your existing images
   */
  public async createFromExistingImage(imageName: string, config: Partial<DockerContainerConfig> = {}): Promise<ContainerInfo> {
    const defaultConfigs: { [key: string]: Partial<DockerContainerConfig> } = {
      'node-microvm': {
        ports: { '3000': '3000', '22': '2222' },
        environment: { NODE_ENV: 'development' },
        restartPolicy: 'unless-stopped'
      },
      'vibekraft/sandbox-base': {
        ports: { '3000': '3000', '5901': '5901', '6080': '6080' },
        environment: { 
          DISPLAY: ':1',
          VNC_PASSWORD: 'vibekraft',
          RESOLUTION: '1280x800'
        },
        privileged: true,
        restartPolicy: 'unless-stopped'
      },
      'desktop-vm': {
        ports: { '5901': '5901', '6080': '6080' },
        environment: {
          DISPLAY: ':1',
          VNC_PASSWORD: 'vncpassword',
          RESOLUTION: '1280x720'
        },
        privileged: true,
        restartPolicy: 'unless-stopped'
      }
    };

    const finalConfig: DockerContainerConfig = {
      image: imageName,
      ...defaultConfigs[imageName],
      ...config
    };

    return this.createContainer(finalConfig);
  }

  /**
   * Helper methods
   */
  private mapDockerStatus(status: string): 'running' | 'stopped' | 'paused' | 'restarting' | 'dead' {
    const statusMap: { [key: string]: any } = {
      'running': 'running',
      'exited': 'stopped',
      'paused': 'paused',
      'restarting': 'restarting',
      'dead': 'dead',
      'created': 'stopped'
    };
    
    return statusMap[status.toLowerCase()] || 'stopped';
  }

  private extractPorts(portsData: any): { [containerPort: string]: string } {
    const ports: { [containerPort: string]: string } = {};
    
    for (const [containerPort, hostBindings] of Object.entries(portsData || {})) {
      if (hostBindings && Array.isArray(hostBindings) && hostBindings.length > 0) {
        ports[containerPort] = hostBindings[0].HostPort;
      }
    }
    
    return ports;
  }

  private parsePorts(portsString: string): { [containerPort: string]: string } {
    const ports: { [containerPort: string]: string } = {};
    
    if (!portsString) return ports;
    
    const portMappings = portsString.split(', ');
    for (const mapping of portMappings) {
      const match = mapping.match(/(\d+):(\d+)/);
      if (match) {
        ports[match[2]] = match[1];
      }
    }
    
    return ports;
  }
}
