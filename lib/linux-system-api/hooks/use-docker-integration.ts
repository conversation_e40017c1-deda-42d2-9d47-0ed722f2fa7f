/**
 * React Hook for Docker Integration with Linux System API
 * 
 * Provides easy access to Docker container management with Linux System API
 */

import { useState, useEffect, useCallback } from 'react';
import { ContainerInfo } from '../docker/docker-integration';

interface UseDockerIntegrationOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
}

interface UseDockerIntegrationReturn {
  // State
  containers: ContainerInfo[];
  isLoading: boolean;
  error: string | null;

  // Container management
  createContainer: (image: string, config?: any) => Promise<ContainerInfo>;
  createFromExistingImage: (imageName: string, config?: any) => Promise<ContainerInfo>;
  startContainer: (containerId: string) => Promise<void>;
  stopContainer: (containerId: string) => Promise<void>;
  restartContainer: (containerId: string) => Promise<void>;
  removeContainer: (containerId: string, force?: boolean) => Promise<void>;
  
  // Container operations
  executeInContainer: (containerId: string, command: string, options?: any) => Promise<any>;
  installPackageInContainer: (containerId: string, packageName: string, options?: any) => Promise<void>;
  getContainerInfo: (containerId: string) => Promise<ContainerInfo>;
  getContainerSystemInfo: (containerId: string) => Promise<any>;
  getContainerLogs: (containerId: string, options?: any) => Promise<string>;

  // Utility functions
  refreshContainers: () => Promise<void>;
}

export function useDockerIntegration(options: UseDockerIntegrationOptions = {}): UseDockerIntegrationReturn {
  const { autoRefresh = true, refreshInterval = 10000 } = options;

  // State
  const [containers, setContainers] = useState<ContainerInfo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // API base URL
  const API_BASE = '/api/linux-system/docker';

  // Helper function to make API calls
  const apiCall = useCallback(async (endpoint: string, options: RequestInit = {}) => {
    try {
      const response = await fetch(`${API_BASE}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      throw err;
    }
  }, []);

  // Refresh containers list
  const refreshContainers = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const data = await apiCall('?action=list&all=true');
      setContainers(data.containers || []);
    } catch (err) {
      console.error('Failed to refresh containers:', err);
    } finally {
      setIsLoading(false);
    }
  }, [apiCall]);

  // Create container
  const createContainer = useCallback(async (image: string, config: any = {}): Promise<ContainerInfo> => {
    const data = await apiCall('', {
      method: 'POST',
      body: JSON.stringify({ 
        action: 'create', 
        image, 
        config 
      }),
    });
    
    await refreshContainers(); // Refresh list after creation
    return data.container;
  }, [apiCall, refreshContainers]);

  // Create from existing image
  const createFromExistingImage = useCallback(async (
    imageName: string, 
    config: any = {}
  ): Promise<ContainerInfo> => {
    const data = await apiCall('', {
      method: 'POST',
      body: JSON.stringify({ 
        action: 'create-from-existing', 
        imageName, 
        containerConfig: config 
      }),
    });
    
    await refreshContainers(); // Refresh list after creation
    return data.container;
  }, [apiCall, refreshContainers]);

  // Start container
  const startContainer = useCallback(async (containerId: string): Promise<void> => {
    await apiCall('', {
      method: 'PUT',
      body: JSON.stringify({ 
        action: 'start', 
        containerId 
      }),
    });
    
    await refreshContainers(); // Refresh list after operation
  }, [apiCall, refreshContainers]);

  // Stop container
  const stopContainer = useCallback(async (containerId: string): Promise<void> => {
    await apiCall('', {
      method: 'PUT',
      body: JSON.stringify({ 
        action: 'stop', 
        containerId 
      }),
    });
    
    await refreshContainers(); // Refresh list after operation
  }, [apiCall, refreshContainers]);

  // Restart container
  const restartContainer = useCallback(async (containerId: string): Promise<void> => {
    await apiCall('', {
      method: 'PUT',
      body: JSON.stringify({ 
        action: 'restart', 
        containerId 
      }),
    });
    
    await refreshContainers(); // Refresh list after operation
  }, [apiCall, refreshContainers]);

  // Remove container
  const removeContainer = useCallback(async (containerId: string, force = false): Promise<void> => {
    await apiCall(`?id=${containerId}&force=${force}`, {
      method: 'DELETE',
    });
    
    await refreshContainers(); // Refresh list after removal
  }, [apiCall, refreshContainers]);

  // Execute command in container
  const executeInContainer = useCallback(async (
    containerId: string, 
    command: string, 
    execOptions: any = {}
  ): Promise<any> => {
    const data = await apiCall('', {
      method: 'POST',
      body: JSON.stringify({ 
        action: 'execute', 
        containerId, 
        command, 
        execOptions 
      }),
    });
    
    return data.result;
  }, [apiCall]);

  // Install package in container
  const installPackageInContainer = useCallback(async (
    containerId: string, 
    packageName: string, 
    packageOptions: any = {}
  ): Promise<void> => {
    await apiCall('', {
      method: 'POST',
      body: JSON.stringify({ 
        action: 'install-package', 
        containerId, 
        packageName, 
        packageOptions 
      }),
    });
  }, [apiCall]);

  // Get container info
  const getContainerInfo = useCallback(async (containerId: string): Promise<ContainerInfo> => {
    const data = await apiCall(`?action=info&id=${containerId}`);
    return data;
  }, [apiCall]);

  // Get container system info
  const getContainerSystemInfo = useCallback(async (containerId: string): Promise<any> => {
    const data = await apiCall(`?action=system-info&id=${containerId}`);
    return data;
  }, [apiCall]);

  // Get container logs
  const getContainerLogs = useCallback(async (
    containerId: string, 
    logOptions: { tail?: number; since?: string } = {}
  ): Promise<string> => {
    const params = new URLSearchParams({
      action: 'logs',
      id: containerId,
      ...(logOptions.tail && { tail: logOptions.tail.toString() }),
      ...(logOptions.since && { since: logOptions.since })
    });
    
    const data = await apiCall(`?${params}`);
    return data.logs;
  }, [apiCall]);

  // Auto-refresh containers on mount
  useEffect(() => {
    refreshContainers();
  }, [refreshContainers]);

  // Set up auto-refresh interval
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(refreshContainers, refreshInterval);
    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, refreshContainers]);

  return {
    // State
    containers,
    isLoading,
    error,

    // Container management
    createContainer,
    createFromExistingImage,
    startContainer,
    stopContainer,
    restartContainer,
    removeContainer,

    // Container operations
    executeInContainer,
    installPackageInContainer,
    getContainerInfo,
    getContainerSystemInfo,
    getContainerLogs,

    // Utility functions
    refreshContainers,
  };
}
